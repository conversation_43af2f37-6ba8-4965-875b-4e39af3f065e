import requests
import os
import time
from dotenv import load_dotenv

load_dotenv()
API_KEY = os.getenv("REMAKER_API_KEY")

BASE_URL = "https://developer.remaker.ai"

def generate_tattoo(prompt, style_key="blackAndGrey", body_part="forearm", complexity="medium", num_outputs=1):
    url = f"{BASE_URL}/api/remaker/v2/ai-tattoo/create-job"
    headers = {
        "Authorization": API_KEY,
        "accept": "application/json"
    }

    # Validate parameters against API documentation
    valid_styles = ["minimalist", "newSchool", "blackAndGrey", "japanese", "neoTraditional",
                   "blackwork", "realism", "trashPolka", "dotwork", "tribal", "surrealism", "watercolor"]
    valid_body_parts = ["forearm", "fullSleeve", "halfSleeve", "thigh", "hand", "shoulder",
                       "wrist", "band", "back", "calf", "chest", "sternum", "finger", "ankle"]
    valid_complexity = ["simple", "medium", "complex"]

    if style_key not in valid_styles:
        print(f"❌ Invalid style_key: {style_key}. Valid options: {valid_styles}")
        return None
    if body_part not in valid_body_parts:
        print(f"❌ Invalid body_part: {body_part}. Valid options: {valid_body_parts}")
        return None
    if complexity not in valid_complexity:
        print(f"❌ Invalid complexity: {complexity}. Valid options: {valid_complexity}")
        return None

    data = {
        "prompt": prompt,
        "style_key": style_key,
        "body_part": body_part,
        "complexity": complexity,
        "num_outputs": num_outputs
    }

    print(f"🔍 Debug - API Key: {API_KEY[:20]}...")
    print(f"🔍 Debug - URL: {url}")
    print(f"🔍 Debug - Data: {data}")

    response = requests.post(url, headers=headers, data=data)

    print(f"🔍 Debug - Status Code: {response.status_code}")
    print(f"🔍 Debug - Response: {response.text}")

    if response.status_code == 200:
        result = response.json()
        print(f"🔍 Debug - Parsed JSON: {result}")
        if result.get("code") == 100000:
            job_id = result["result"]["job_id"]
            print(f"✅ Job submitted. Job ID: {job_id}")
            return job_id
        else:
            print(f"❌ API Error: {result.get('message', {}).get('en', 'Unknown error')}")
            print(f"🔍 Debug - Full result: {result}")
            return None
    else:
        print(f"❌ HTTP Error {response.status_code}: {response.text}")
        return None

def check_status(job_id):
    url = f"{BASE_URL}/api/remaker/v2/ai-tattoo/get-job/{job_id}"
    headers = {
        "Authorization": API_KEY,  
        "accept": "application/json"
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 100000:  
            output = result["result"].get("output")
            if output and len(output) > 0:
                print(f"🎉 Tattoo Ready! Generated {len(output)} image(s):")
                for i, image_url in enumerate(output, 1):
                    print(f"   Image {i}: {image_url}")
                return output
            else:
                print("⌛ Still processing. Try again later.")
                return None
        else:
            print(f"❌ API Error: {result.get('message', {}).get('en', 'Unknown error')}")
            return None
    else:
        print(f"❌ HTTP Error {response.status_code}: {response.text}")
        return None

if __name__ == "__main__":
    user_prompt = "a floral tattoo"
    # Using valid parameters from the API documentation
    job_id = generate_tattoo(user_prompt, style_key="blackAndGrey", body_part="forearm", complexity="medium")

    if job_id:
        print("⏳ Waiting 15 seconds before checking status...")
        time.sleep(15)
        images = check_status(job_id)

        if images is None:
            print("⏳ Waiting another 15 seconds...")
            time.sleep(15)
            check_status(job_id)
