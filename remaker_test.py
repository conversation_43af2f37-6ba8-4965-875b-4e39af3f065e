import requests
import os
import time
from dotenv import load_dotenv

# Load the API key from .env
load_dotenv()
API_KEY = os.getenv("REMAKER_API_KEY")

BASE_URL = "https://api.remaker.ai"

def generate_tattoo(prompt, style="blackAndGrey", body_part="arm", complexity="medium"):
    url = f"{BASE_URL}/ai-tattoo/generate"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    data = {
        "prompt": prompt,
        "style": style,
        "body_part": body_part,
        "complexity": complexity
    }

    response = requests.post(url, json=data, headers=headers)
    
    if response.status_code == 200:
        job_id = response.json().get("job_id")
        print(f"✅ Job submitted. Job ID: {job_id}")
        return job_id
    else:
        print(f"❌ Error submitting job: {response.text}")
        return None

def check_status(job_id):
    url = f"{BASE_URL}/ai-tattoo/check-status/{job_id}"
    headers = {
        "Authorization": f"Bearer {API_KEY}"
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        print(f"🔄 Status: {data['status']}")
        if data["status"] == "completed":
            image_url = data["images"][0]["url"]
            print(f"🎉 Tattoo Ready! Image URL: {image_url}")
        else:
            print("⌛ Still processing. Try again later.")
    else:
        print(f"❌ Error checking status: {response.text}")

# ------------ RUN TEST -------------
if __name__ == "__main__":
    user_prompt = "a phoenix rising from fire with tribal design"
    job_id = generate_tattoo(user_prompt)

    if job_id:
        print("⏳ Waiting 10 seconds before checking status...")
        time.sleep(10)
        check_status(job_id)
