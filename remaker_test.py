import requests
import os
import time
from dotenv import load_dotenv

load_dotenv()
API_KEY = os.getenv("REMAKER_API_KEY")

BASE_URL = "https://developer.remaker.ai"

def generate_tattoo(prompt, style_key="blackAndGrey", body_part="arm", complexity="medium", num_outputs=1):
    url = f"{BASE_URL}/api/remaker/v2/ai-tattoo/create-job"
    headers = {
        "Authorization": API_KEY, 
        "accept": "application/json"
    }
    data = {
        "prompt": prompt,
        "style_key": style_key,  
        "body_part": body_part,
        "complexity": complexity,
        "num_outputs": num_outputs
    }

    response = requests.post(url, headers=headers, data=data)  

    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 100000: 
            job_id = result["result"]["job_id"]
            print(f"✅ Job submitted. Job ID: {job_id}")
            return job_id
        else:
            print(f"❌ API Error: {result.get('message', {}).get('en', 'Unknown error')}")
            return None
    else:
        print(f"❌ HTTP Error {response.status_code}: {response.text}")
        return None

def check_status(job_id):
    url = f"{BASE_URL}/api/remaker/v2/ai-tattoo/get-job/{job_id}"
    headers = {
        "Authorization": API_KEY,  
        "accept": "application/json"
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 100000:  
            output = result["result"].get("output")
            if output and len(output) > 0:
                print(f"🎉 Tattoo Ready! Generated {len(output)} image(s):")
                for i, image_url in enumerate(output, 1):
                    print(f"   Image {i}: {image_url}")
                return output
            else:
                print("⌛ Still processing. Try again later.")
                return None
        else:
            print(f"❌ API Error: {result.get('message', {}).get('en', 'Unknown error')}")
            return None
    else:
        print(f"❌ HTTP Error {response.status_code}: {response.text}")
        return None

if __name__ == "__main__":
    user_prompt = ""
    job_id = generate_tattoo(user_prompt, style_key="tribal", body_part="forearm", complexity="medium")

    if job_id:
        print("⏳ Waiting 15 seconds before checking status...")
        time.sleep(15)
        images = check_status(job_id)

        if images is None:
            print("⏳ Waiting another 15 seconds...")
            time.sleep(15)
            check_status(job_id)
